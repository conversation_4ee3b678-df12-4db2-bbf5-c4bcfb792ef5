"""latest version of SuperpointNet. Use it!

"""

import torch
import torch.nn as nn
from torch.nn.init import xavier_uniform_, zeros_
from models.unet_parts import *
import numpy as np

# from models.SubpixelNet import SubpixelNet
import torch
import torch.nn as nn
from torch.nn.init import xavier_uniform_, zeros_
import numpy as np


class h_sigmoid(nn.Module):
    def __init__(self, inplace=True):
        super(h_sigmoid, self).__init__()
        self.relu = nn.ReLU6(inplace=inplace)

    def forward(self, x):
        return self.relu(x + 3) / 6


class h_swish(nn.Module):
    def __init__(self, inplace=True):
        super(h_swish, self).__init__()
        self.sigmoid = h_sigmoid(inplace=inplace)

    def forward(self, x):
        return x * self.sigmoid(x)


class SELayer(nn.Module):
    def __init__(self, channel, reduction=4):
        super(<PERSON><PERSON>ayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),  # 改为ReLU
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()  # 改为Sigmoid，更稳定
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)


class InvertedResidual(nn.Module):
    def __init__(self, inp, hidden_dim, oup, kernel_size, stride, use_se, use_hs):
        super(InvertedResidual, self).__init__()
        assert stride in [1, 2]

        self.identity = stride == 1 and inp == oup

        if inp == hidden_dim:
            self.conv = nn.Sequential(
                # dw
                nn.Conv2d(hidden_dim, hidden_dim, kernel_size, stride,
                          (kernel_size - 1) // 2, groups=hidden_dim, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.ReLU(inplace=True),
                # Squeeze-and-Excite
                SELayer(hidden_dim, reduction=8) if use_se else nn.Identity(),
                # pw-linear
                nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
                nn.BatchNorm2d(oup),
            )
        else:
            self.conv = nn.Sequential(
                # pw
                nn.Conv2d(inp, hidden_dim, 1, 1, 0, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.ReLU(inplace=True),
                # dw
                nn.Conv2d(hidden_dim, hidden_dim, kernel_size, stride,
                          (kernel_size - 1) // 2, groups=hidden_dim, bias=False),
                nn.BatchNorm2d(hidden_dim),
                nn.ReLU(inplace=True),
                # Squeeze-and-Excite
                SELayer(hidden_dim, reduction=8) if use_se else nn.Identity(),
                # pw-linear
                nn.Conv2d(hidden_dim, oup, 1, 1, 0, bias=False),
                nn.BatchNorm2d(oup),
            )

    def forward(self, x):
        if self.identity:
            return x + self.conv(x)
        else:
            return self.conv(x)


# SuperPoint风格的卷积块（用于1/2和1/4下采样）
class SuperPointBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1):
        super(SuperPointBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)

        # # 残差连接
        # if stride != 1 or in_channels != out_channels:
        #     self.shortcut = nn.Sequential(
        #         nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
        #         nn.BatchNorm2d(out_channels)
        #     )
        # else:
        #     self.shortcut = nn.Identity()

    def forward(self, x):
        # residual = self.shortcut(x)

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        # out += residual
        out = self.relu(out)

        return out

class SuperPointNet_gauss2(torch.nn.Module):
    """ SuperPoint Network with improved architecture. """

    def __init__(self, subpixel_channel=1):
        super(SuperPointNet_gauss2, self).__init__()

        self.initial_conv = nn.Sequential(
            nn.Conv2d(1, 32, 5, padding=2),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 64, 5, padding=2),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )

        # 64→128 (1/2) - 2个InvertedResidual块
        self.down_1_2 = nn.Sequential(
            InvertedResidual(64, 64*2, 128, kernel_size=3, stride=2, use_se=True, use_hs=False),  # 下采样，expansion=4
            InvertedResidual(128, 128*2, 128, kernel_size=3, stride=1, use_se=True, use_hs=False),  # refinement，expansion=2
        )

        # 128→256 (1/4) - 2个InvertedResidual块
        self.down_1_4 = nn.Sequential(
            InvertedResidual(128, 128*2, 256, kernel_size=3, stride=2, use_se=True, use_hs=False),  # 下采样，expansion=4
            InvertedResidual(256, 256*2, 256, kernel_size=3, stride=1, use_se=True, use_hs=False),  # refinement，expansion=2
        )

        # 256→256 (1/8) - 2个InvertedResidual块
        self.down_1_8 = nn.Sequential(
            InvertedResidual(256, 256*2, 256, kernel_size=3, stride=2, use_se=True, use_hs=False),  # 下采样，expansion=4
            InvertedResidual(256, 256*2, 256, kernel_size=3, stride=1, use_se=True, use_hs=False),  # refinement，expansion=2
        )

        # 特征维度（保持原有设定）
        c4 = 256  # 更新为最终输出通道数
        c5 = 256  # Hidden dimension for heads
        d1 = 256  # Descriptor dimension
        det_h = 65  # Detection head output

        self.relu = torch.nn.ReLU(inplace=True)

        # Detector Head（保持原有结构）
        self.convPa = torch.nn.Conv2d(c4, c5, kernel_size=3, stride=1, padding=1, bias=False)
        self.bnPa = nn.BatchNorm2d(c5)
        self.convPb = torch.nn.Conv2d(c5, det_h, kernel_size=1, stride=1, padding=0, bias=False)
        self.bnPb = nn.BatchNorm2d(det_h)

        # Descriptor Head（保持原有结构）
        self.convDa = torch.nn.Conv2d(c4, c5, kernel_size=3, stride=1, padding=1, bias=False)
        self.bnDa = nn.BatchNorm2d(c5)
        self.convDb = torch.nn.Conv2d(c5, d1, kernel_size=1, stride=1, padding=0, bias=False)
        self.bnDb = nn.BatchNorm2d(d1)

        self.output = None

        self._initialize_weights()

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                # 使用Kaiming初始化，适合ReLU
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        """ Forward pass that jointly computes unprocessed point and descriptor
        tensors.
        Input
          x: Image pytorch tensor shaped N x 1 x patch_size x patch_size.
        Output
          semi: Output point pytorch tensor shaped N x 65 x H/8 x W/8.
          desc: Output descriptor pytorch tensor shaped N x 256 x H/8 x W/8.
        """

        # 初始特征提取 - 保持原有方法
        x = self.initial_conv(x)  # 1→64

        # MobileNetV3风格的编码器
        x = self.down_1_2(x)  # 64→128 (1/2)
        x = self.down_1_4(x)  # 128→256 (1/4)
        x = self.down_1_8(x)  # 256→256 (1/8)

        # Detector Head（保持原有结构）
        cPa = self.relu(self.bnPa(self.convPa(x)))
        semi = self.bnPb(self.convPb(cPa))

        # Descriptor Head（保持原有结构）
        cDa = self.relu(self.bnDa(self.convDa(x)))
        desc = self.bnDb(self.convDb(cDa))

        # Normalize descriptors（保持原有归一化，但添加数值稳定性）
        dn = torch.norm(desc, p=2, dim=1)  # Compute the norm
        desc = desc.div(torch.unsqueeze(dn + 1e-8, 1))  # 添加小常数避免除零

        output = {'semi': semi, 'desc': desc}
        self.output = output

        return output

    def process_output(self, sp_processer):
        from utils.utils import flattenDetection
        output = self.output
        semi = output['semi']
        desc = output['desc']
        # flatten
        heatmap = flattenDetection(semi)  # [batch_size, 1, H, W]
        # nms
        heatmap_nms_batch = sp_processer.heatmap_to_nms(heatmap, tensor=True)
        # extract offsets
        outs = sp_processer.pred_soft_argmax(heatmap_nms_batch, heatmap)
        residual = outs['pred']
        # extract points
        outs = sp_processer.batch_extract_features(desc, heatmap_nms_batch, residual)

        output.update(outs)
        self.output = output
        return output


# # Modified V1
# class SuperPointNet_gauss2(torch.nn.Module):
#     """ SuperPoint Network with improved architecture. """
#
#     def __init__(self, subpixel_channel=1):
#         super(SuperPointNet_gauss2, self).__init__()
#
#         self.initial_conv = inconv(1, 64)
#
#         # 1/2下采样 - 使用SuperPoint方法
#         self.down_1_2 = SuperPointBlock(64, 64, stride=2)
#
#         # 1/4下采样 - 使用SuperPoint方法
#         self.down_1_4 = SuperPointBlock(64, 128, stride=2)
#
#         # 1/8下采样 - 使用Inverted Residual
#         self.down_1_8 = nn.Sequential(
#             InvertedResidual(128, 256, 128, kernel_size=3, stride=2, use_se=True, use_hs=False),
#             InvertedResidual(128, 256, 128, kernel_size=3, stride=1, use_se=True, use_hs=False),
#         )
#
#
#         # 特征维度（保持原有设定）
#         c4 = 128  # Output from adapter
#         c5 = 256  # Hidden dimension for heads
#         d1 = 256  # Descriptor dimension
#         det_h = 65  # Detection head output
#
#         self.relu = torch.nn.ReLU(inplace=True)
#
#         # Detector Head（保持原有结构）
#         self.convPa = torch.nn.Conv2d(c4, c5, kernel_size=3, stride=1, padding=1, bias=False)
#         self.bnPa = nn.BatchNorm2d(c5)
#         self.convPb = torch.nn.Conv2d(c5, det_h, kernel_size=1, stride=1, padding=0, bias=False)
#         self.bnPb = nn.BatchNorm2d(det_h)
#
#         # Descriptor Head（保持原有结构）
#         self.convDa = torch.nn.Conv2d(c4, c5, kernel_size=3, stride=1, padding=1, bias=False)
#         self.bnDa = nn.BatchNorm2d(c5)
#         self.convDb = torch.nn.Conv2d(c5, d1, kernel_size=1, stride=1, padding=0, bias=False)
#         self.bnDb = nn.BatchNorm2d(d1)
#
#         self.output = None
#
#         # 改进的权重初始化
#         self._initialize_weights()
#
#     def _initialize_weights(self):
#         """改进的权重初始化，防止梯度爆炸"""
#         for m in self.modules():
#             if isinstance(m, nn.Conv2d):
#                 # 使用Kaiming初始化，适合ReLU
#                 nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
#                 if m.bias is not None:
#                     nn.init.zeros_(m.bias)
#             elif isinstance(m, nn.BatchNorm2d):
#                 nn.init.ones_(m.weight)
#                 nn.init.zeros_(m.bias)
#             elif isinstance(m, nn.Linear):
#                 nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
#                 if m.bias is not None:
#                     nn.init.zeros_(m.bias)
#
#     def forward(self, x):
#         """ Forward pass that jointly computes unprocessed point and descriptor
#         tensors.
#         Input
#           x: Image pytorch tensor shaped N x 1 x patch_size x patch_size.
#         Output
#           semi: Output point pytorch tensor shaped N x 65 x H/8 x W/8.
#           desc: Output descriptor pytorch tensor shaped N x 256 x H/8 x W/8.
#         """
#
#         # 初始特征提取
#         x = self.initial_conv(x)
#
#         x = self.down_1_2(x)  # 1/2下采样，SuperPoint方法
#         x = self.down_1_4(x)  # 1/4下采样，SuperPoint方法
#         x = self.down_1_8(x)  # 1/8下采样，Inverted Residual方法
#
#
#         # Detector Head（保持原有结构）
#         cPa = self.relu(self.bnPa(self.convPa(x)))
#         semi = self.bnPb(self.convPb(cPa))
#
#         # Descriptor Head（保持原有结构）
#         cDa = self.relu(self.bnDa(self.convDa(x)))
#         desc = self.bnDb(self.convDb(cDa))
#
#         # Normalize descriptors（保持原有归一化，但添加数值稳定性）
#         dn = torch.norm(desc, p=2, dim=1)  # Compute the norm
#         desc = desc.div(torch.unsqueeze(dn + 1e-8, 1))  # 添加小常数避免除零
#
#         output = {'semi': semi, 'desc': desc}
#         self.output = output
#
#         return output
#
#
#     def process_output(self, sp_processer):
#         """保持原有的输出处理逻辑"""
#         from utils.utils import flattenDetection
#         output = self.output
#         semi = output['semi']
#         desc = output['desc']
#         # flatten
#         heatmap = flattenDetection(semi)  # [batch_size, 1, H, W]
#         # nms
#         heatmap_nms_batch = sp_processer.heatmap_to_nms(heatmap, tensor=True)
#         # extract offsets
#         outs = sp_processer.pred_soft_argmax(heatmap_nms_batch, heatmap)
#         residual = outs['pred']
#         # extract points
#         outs = sp_processer.batch_extract_features(desc, heatmap_nms_batch, residual)
#
#         output.update(outs)
#         self.output = output
#         return output


class SuperPointNet_gauss2_org(torch.nn.Module):
    """ Pytorch definition of SuperPoint Network. """
    def __init__(self, subpixel_channel=1):
        super(SuperPointNet_gauss2_org, self).__init__()
        c1, c2, c3, c4, c5, d1 = 64, 64, 128, 128, 256, 256
        det_h = 65
        self.inc = inconv(1, c1)
        self.down1 = down(c1, c2)
        self.down2 = down(c2, c3)
        self.down3 = down(c3, c4)
        # self.down4 = down(c4, 512)
        # self.up1 = up(c4+c3, c2)
        # self.up2 = up(c2+c2, c1)
        # self.up3 = up(c1+c1, c1)
        # self.outc = outconv(c1, subpixel_channel)
        self.relu = torch.nn.ReLU(inplace=True)
        # self.outc = outconv(64, n_classes)
        # Detector Head.
        self.convPa = torch.nn.Conv2d(c4, c5, kernel_size=3, stride=1, padding=1)
        self.bnPa = nn.BatchNorm2d(c5)
        self.convPb = torch.nn.Conv2d(c5, det_h, kernel_size=1, stride=1, padding=0)
        self.bnPb = nn.BatchNorm2d(det_h)
        # Descriptor Head.
        self.convDa = torch.nn.Conv2d(c4, c5, kernel_size=3, stride=1, padding=1)
        self.bnDa = nn.BatchNorm2d(c5)
        self.convDb = torch.nn.Conv2d(c5, d1, kernel_size=1, stride=1, padding=0)
        self.bnDb = nn.BatchNorm2d(d1)
        self.output = None



    def forward(self, x):
        """ Forward pass that jointly computes unprocessed point and descriptor
        tensors.
        Input
          x: Image pytorch tensor shaped N x 1 x patch_size x patch_size.
        Output
          semi: Output point pytorch tensor shaped N x 65 x H/8 x W/8.
          desc: Output descriptor pytorch tensor shaped N x 256 x H/8 x W/8.
        """
        # Let's stick to this version: first BN, then relu
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)

        # Detector Head.
        cPa = self.relu(self.bnPa(self.convPa(x4)))
        semi = self.bnPb(self.convPb(cPa))
        # Descriptor Head.
        cDa = self.relu(self.bnDa(self.convDa(x4)))
        desc = self.bnDb(self.convDb(cDa))

        dn = torch.norm(desc, p=2, dim=1) # Compute the norm.
        desc = desc.div(torch.unsqueeze(dn, 1)) # Divide by norm to normalize.
        output = {'semi': semi, 'desc': desc}
        self.output = output

        return output

    def process_output(self, sp_processer):
        """
        input:
          N: number of points
        return: -- type: tensorFloat
          pts: tensor [batch, N, 2] (no grad)  (x, y)
          pts_offset: tensor [batch, N, 2] (grad) (x, y)
          pts_desc: tensor [batch, N, 256] (grad)
        """
        from utils.utils import flattenDetection
        # from models.model_utils import pred_soft_argmax, sample_desc_from_points
        output = self.output
        semi = output['semi']
        desc = output['desc']
        # flatten
        heatmap = flattenDetection(semi) # [batch_size, 1, H, W]
        # nms
        heatmap_nms_batch = sp_processer.heatmap_to_nms(heatmap, tensor=True)
        # extract offsets
        outs = sp_processer.pred_soft_argmax(heatmap_nms_batch, heatmap)
        residual = outs['pred']
        # extract points
        outs = sp_processer.batch_extract_features(desc, heatmap_nms_batch, residual)

        # output.update({'heatmap': heatmap, 'heatmap_nms': heatmap_nms, 'descriptors': descriptors})
        output.update(outs)
        self.output = output
        return output


def get_matches(deses_SP):
    from models.model_wrap import PointTracker
    tracker = PointTracker(max_length=2, nn_thresh=1.2)
    f = lambda x: x.cpu().detach().numpy()
    # tracker = PointTracker(max_length=2, nn_thresh=1.2)
    # print("deses_SP[1]: ", deses_SP[1].shape)
    matching_mask = tracker.nn_match_two_way(f(deses_SP[0]).T, f(deses_SP[1]).T, nn_thresh=1.2)
    return matching_mask

    # print("matching_mask: ", matching_mask.shape)
    # f_mask = lambda pts, maks: pts[]
    # pts_m = []
    # pts_m_res = []
    # for i in range(2):
    #     idx = xs_SP[i][matching_mask[i, :].astype(int), :]
    #     res = reses_SP[i][matching_mask[i, :].astype(int), :]
    #     print("idx: ", idx.shape)
    #     print("res: ", idx.shape)
    #     pts_m.append(idx)
    #     pts_m_res.append(res)
    #     pass

    # pts_m = torch.cat((pts_m[0], pts_m[1]), dim=1)
    # matches_test = toNumpy(pts_m)
    # print("pts_m: ", pts_m.shape)

    # pts_m_res = torch.cat((pts_m_res[0], pts_m_res[1]), dim=1)
    # # pts_m_res = toNumpy(pts_m_res)
    # print("pts_m_res: ", pts_m_res.shape)
    # # print("pts_m_res: ", pts_m_res)
        
    # pts_idx_res = torch.cat((pts_m, pts_m_res), dim=1)
    # print("pts_idx_res: ", pts_idx_res.shape)

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SuperPointNet_gauss2()
    # model = SuperPointNet_gauss2_org()
    model = model.to(device)


    # check keras-like model summary using torchsummary
    from torchsummary import summary
    summary(model, input_size=(1, 240, 320))

    ## test
    image = torch.zeros((2,1,120, 160))
    outs = model(image.to(device))
    print("outs: ", list(outs))

    from utils.print_tool import print_dict_attr
    print_dict_attr(outs, 'shape')

    from models.model_utils import SuperPointNet_process 
    params = {
        'out_num_points': 500,
        'patch_size': 5,
        'device': device,
        'nms_dist': 4,
        'conf_thresh': 0.015
    }

    sp_processer = SuperPointNet_process(**params)
    outs = model.process_output(sp_processer)
    print("outs: ", list(outs))
    print_dict_attr(outs, 'shape')

    # timer
    import time
    from tqdm import tqdm
    iter_max = 50

    start = time.time()
    print("Start timer!")
    for i in tqdm(range(iter_max)):
        outs = model(image.to(device))
    end = time.time()
    print("forward only: ", iter_max/(end - start), " iter/s")

    start = time.time()
    print("Start timer!")
    xs_SP, deses_SP, reses_SP = [], [], []
    for i in tqdm(range(iter_max)):
        outs = model(image.to(device))
        outs = model.process_output(sp_processer)
        xs_SP.append(outs['pts_int'].squeeze())
        deses_SP.append(outs['pts_desc'].squeeze())
        reses_SP.append(outs['pts_offset'].squeeze())
    end = time.time()
    print("forward + process output: ", iter_max/(end - start), " iter/s")

    start = time.time()
    print("Start timer!")
    for i in tqdm(range(len(xs_SP))):
        get_matches([deses_SP[i][0], deses_SP[i][1]])
    end = time.time()
    print("nn matches: ", iter_max/(end - start), " iters/s")


if __name__ == '__main__':
    main()



