data:
    name: 'kitti'
    dataset: 'Kitti_inh' # 'Kitti'
    root:  './datasets/KITTI/' # /data/kitti/kitti_wVal
    root_split_txt: 'datasets/kitti_split'   
    labels: logs/magicpoint_synth20_homoAdapt100_kitti_h384_labels/predictions

    gaussian_label:
        enable: true
        params:
            GaussianBlur: {sigma: 0.2}

    cache_in_memory: false
    preprocessing:
        resize: [384, 1248]  # hand defined, original: [375, 1242]
        # resize: [192, 624]
    augmentation:
        photometric:
            enable: true
            primitives: [
                'random_brightness', 'random_contrast', 'additive_speckle_noise',
                'additive_gaussian_noise', 'additive_shade', 'motion_blur']
            params:
                random_brightness: {max_abs_change: 50}
                random_contrast: {strength_range: [0.5, 1.5]}
                additive_gaussian_noise: {stddev_range: [0, 10]}
                additive_speckle_noise: {prob_range: [0, 0.0035]}
                additive_shade:
                    transparency_range: [-0.5, 0.5]
                    kernel_size_range: [100, 150]
                motion_blur: {max_kernel_size: 3}
        homographic:
            enable: false  # not implemented
            params:
                {}
    warped_pair:
        enable: true
        params:
            translation: true
            rotation: true
            scaling: true
            perspective: true
            scaling_amplitude: 0.2
            perspective_amplitude_x: 0.2
            perspective_amplitude_y: 0.2
            patch_ratio: 0.85
            max_angle: 1.57 # 0.1
            allow_artifacts: true # true
        valid_border_margin: 3


front_end_model: 'Train_model_heatmap'  # 'Train_model_frontend'

training:
    workers_train: 4 # 16
    workers_val: 2 # 2

model:
    name: 'SuperPointNet_gauss2'
    params: {
    }
    detector_loss:
        loss_type: 'softmax'


    batch_size: 4 # 32
    eval_batch_size: 4
    learning_rate: 0.0001
    detection_threshold: 0.015 # 0.015
    nms: 4
    lambda_loss: 1
    # top_k: 300
    dense_loss:
        enable: false
        params:
            descriptor_dist: 4 # 4, 7.5
            lambda_d: 800 # 800
    sparse_loss:
        enable: true
        params:
            num_matching_attempts: 600
            num_masked_non_matches_per_match: 100
            lamda_d: 1
            dist: 'cos'
            method: '2d'
            other_settings: 'train from scratch, 2d method'
    lambda_res: 100

retrain: true  # set true for new model
reset_iter: true
train_iter: 170000
validation_interval: 2000
tensorboard_interval: 400
save_interval: 2000
validation_size: 5

pretrained:
# pretrained: 'logs/superpoint_coco_heat2_0/checkpoints/superPointNet_90000_checkpoint.pth.tar'


