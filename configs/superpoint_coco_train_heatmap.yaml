data:
    # name: 'coco'
    dataset: 'Coco' # 'coco'

    labels: logs/magicpoint_synth_homoAdapt_coco/predictions
    root: # datasets/COCO
    root_split_txt: # /datasets/COCO

    gaussian_label:
        enable: true
        params:
            GaussianBlur: {sigma: 0.2}


    cache_in_memory: false
    preprocessing:
        # resize: [240, 320]
        resize: [480, 640]
    augmentation:
        photometric:
            enable: true
            primitives: [
                'random_brightness', 'random_contrast', 'additive_speckle_noise',
                'additive_gaussian_noise', 'additive_shade', 'motion_blur']
            params:
                random_brightness: {max_abs_change: 60}
                random_contrast: {strength_range: [0.25, 1.75]}
                additive_gaussian_noise: {stddev_range: [0, 12]}
                additive_speckle_noise: {prob_range: [0, 0.006]}
                additive_shade:
                    transparency_range: [-0.75, 0.75]
                    kernel_size_range: [75, 175]
                motion_blur: {max_kernel_size: 9}
        homographic:
            enable: false  # not implemented
    warped_pair:
        enable: true
        params:
            translation: true
            rotation: true
            scaling: true
            perspective: true
            scaling_amplitude: 0.4
            perspective_amplitude_x: 0.4
            perspective_amplitude_y: 0.4
            patch_ratio: 0.85
            max_angle: 1.95
            allow_artifacts: true # true
        valid_border_margin: 6

front_end_model: 'Train_model_heatmap'  # 'Train_model_frontend'

training:
    workers_train: 4 # 16
    workers_val: 2 # 2

model:
    # name: 'magic_point'
    # name: 'SuperPointNet_heatmap'
    name: 'SuperPointNet_gauss2'
    params: {
    }
    detector_loss:
        loss_type: 'softmax'


    batch_size: 5 # 32
    eval_batch_size: 8 # 32
    learning_rate: 0.0001 # 0.0001
    detection_threshold: 0.005 # 0.015
    lambda_loss: 1 # 1
    nms: 4
    dense_loss:
        enable: false
        params:
            descriptor_dist: 4 # 4, 7.5
            lambda_d: 800 # 800
    sparse_loss:
        enable: true
        params:
            num_matching_attempts: 1200
            num_masked_non_matches_per_match: 120
            lamda_d: 1
            dist: 'cos' # cos euclidean
            method: '2d'
    other_settings: 'train 2d, gauss 0.2'
    # subpixel:
        # enable: false
        # params:
        #     subpixel_channel: 2
        # settings: 'predict flow directly'
        # loss_func: 'subpixel_loss_no_argmax' # subpixel_loss, subpixel_loss_no_argmax

retrain: false # set true for new model
reset_iter: true # set true to set the iteration number to 0
train_iter: 500000 # 170000
validation_interval: 5000 # 2000
tensorboard_interval: 100 # 200
save_interval: 1000 # 2000
validation_size: 5

# pretrained: '/home/<USER>/ADaryl/Codes/Python/superpoint22222/pytorch-superpoint22222222/logs/superpoint_coco_heat2_0/checkpoints/superPointNet_170000_checkpoint.pth.tar'

# pretrained: '/home/<USER>/ADaryl/Codes/Python/superpoint22222/pytorch-superpoint22222222/logs/superpoint_coco/checkpoints/superPointNet_144000_checkpoint.pth.tar'

# pretrained: '/home/<USER>/ADaryl/Codes/Python/superpoint22222/pytorch-superpoint22222222/logs/superpoint_coco/checkpoints/superPointNet_600_checkpoint.pth.tar'

# pretrained: '/home/<USER>/ADaryl/Codes/Python/superpoint22222/pytorch-superpoint22222222/logs/magicpoint_synth/checkpoints/superPointNet_66500_checkpoint.pth.tar'

pretrained : '/home/<USER>/ADaryl/Codes/Python/superpoint22222/pytorch-superpoint22222222/logs/superpoint_coco/checkpoints/superPointNet_50722_checkpoint.pth.tar'