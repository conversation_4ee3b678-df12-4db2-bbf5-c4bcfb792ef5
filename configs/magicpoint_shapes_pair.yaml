data:
#    name: 'synthetic_shapes'
    dataset: 'SyntheticDataset_gaussian'
    primitives: 'all'
    truncate: {draw_ellipses: 0.3, draw_stripes: 0.2, gaussian_noise: 0.1}
    cache_in_memory: true
    suffix: 'v6'
    add_augmentation_to_test_set: false  # set to true to evaluate with noise
    gaussian_label:
        enable: false
        params:
            GaussianBlur: {sigma: 0.2}
    preprocessing: ## didn't do this
        blur_size: 21
        resize: [480, 640]
    augmentation:
        photometric:
            enable: true
            enable_train: true
            enable_val: false
            primitives: [
                'random_brightness', 'random_contrast', 'additive_speckle_noise',
                'additive_gaussian_noise', 'additive_shade', 'motion_blur' ]
            params:
                random_brightness: {max_abs_change: 75}
                random_contrast: {strength_range: [0.3, 1.8]}
                additive_gaussian_noise: {stddev_range: [0, 15]}
                additive_speckle_noise: {prob_range: [0, 0.0035]}
                additive_shade:
                    transparency_range: [-0.5, 0.8]
                    kernel_size_range: [50, 100]
                motion_blur: {max_kernel_size: 7} # origin 7
        homographic:
            enable: true
            enable_train: true
            enable_val: false
            params:
                translation: true
                rotation: true
                scaling: true
                perspective: true
                scaling_amplitude: 0.2
                perspective_amplitude_x: 0.2
                perspective_amplitude_y: 0.2
                patch_ratio: 0.8
                max_angle: 1.57  # 3.14
                allow_artifacts: true
                translation_overflow: 0.05
            valid_border_margin: 2
    warped_pair:
        enable: false # false when training only on detector
        params:
            translation: true
            rotation: true
            scaling: true
            perspective: true
            scaling_amplitude: 0.2
            perspective_amplitude_x: 0.2
            perspective_amplitude_y: 0.2
            patch_ratio: 0.85
            max_angle: 1.57
            allow_artifacts: true # true
        valid_border_margin: 3

front_end_model: 'Train_model_heatmap'  # 'Train_model_frontend'

model:
    name: 'SuperPointNet_gauss2'
    params: {
    }
    detector_loss:
        loss_type: 'softmax'

    batch_size:  64 # 64
    eval_batch_size: 16
    learning_rate: 0.001
    kernel_reg: 0.
    detection_threshold: 0.001 # 1/65
    nms: 4
    lambda_loss: 0 # disable descriptor loss
    dense_loss:
        enable: false
        params:
            descriptor_dist: 4 # 4, 7.5
            lambda_d: 800 # 800
    sparse_loss:
        enable: true
        params:
            num_matching_attempts: 1000
            num_masked_non_matches_per_match: 100
            lamda_d: 1
            dist: 'cos'
            method: '2d'
    other_settings: 'train 2d, gauss 0.5'

retrain: true # set true for new model
reset_iter: True

train_iter: 100000 # 200000
tensorboard_interval: 100 # 200
save_interval: 500 # 2000
validation_interval: 1000 # one validation of entire val set every N training steps
validation_size: 1000
train_show_interval: 1000 # one show of the current training from to Tensorboard every N training steps
seed: 42

pretrained: '/home/<USER>/ADaryl/Codes/Python/superpoint_pytorch/superpoint/logs/magicpoint_synth_t2/checkpoints/superPointNet_100000_checkpoint.pth.tar'
