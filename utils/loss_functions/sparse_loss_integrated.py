import torch
import numpy as np
from typing import <PERSON><PERSON>, Dict, Optional


class SparseLoss:
    """Integrated sparse loss module with batch support"""

    def __init__(self, config: Dict = None):
        """
        Initialize sparse loss module

        Args:
            config: Configuration parameters with optional fields:
                - lamda_d: matching loss weight (default: 250)
                - margin_pos: positive sample margin (default: 1)
                - margin_neg: negative sample margin (default: 0.2)
                - num_matching_attempts: non-matching point sampling attempts (default: 1000)
                - num_masked_non_matches_per_match: non-matching points per match (default: 10)
        """
        # Default parameters
        self.params = dict(
            lamda_d=250,
            margin_pos=1,
            margin_neg=0.2,
            num_matching_attempts=1000,
            num_masked_non_matches_per_match=10,
            dist='cos'
        )

        # Update configuration
        if config:
            self.params.update(config)

    def compute_loss(self,
                     descriptors: torch.Tensor,
                     descriptors_warped: torch.Tensor,
                     homographies: torch.Tensor,
                     mask_valid: Optional[torch.Tensor] = None,
                     cell_size: int = 8,
                     device: str = 'cpu') -> Tuple[torch.Tensor, Dict]:
        """
        Compute complete sparse loss with batch support

        Args:
            descriptors: Original descriptor tensor [B, D, Hc, Wc]
            descriptors_warped: Warped descriptor tensor [B, D, Hc, Wc]
            homographies: Homography matrices [B, 3, 3]
            mask_valid: Valid region mask [B, Hc, Wc] (optional)
            cell_size: Grid size
            device: Computing device ('cpu' or 'cuda')

        Returns:
            loss: Total loss (scalar)
            loss_components: Dictionary containing loss components
        """
        # Get dimensions
        B, D, Hc, Wc = descriptors.shape

        # Initialize loss accumulators
        total_match_loss = torch.tensor(0.0, device=device)
        total_non_match_loss = torch.tensor(0.0, device=device)

        # Process each batch item
        for b in range(B):
            # Extract batch item
            desc_b = descriptors[b]  # [D, Hc, Wc]
            desc_warped_b = descriptors_warped[b]  # [D, Hc, Wc]
            homo_b = homographies[b]  # [3, 3]

            # 1. Generate matching points for this batch item
            uv_a, uv_b_matches = self._generate_matching_points(
                Hc, Wc, homo_b, cell_size, device)

            # 2. Generate non-matching points
            uv_a_masked, uv_b_masked = self._generate_non_matches(
                uv_a, uv_b_matches, Hc, Wc, device)

            # 3. Compute matching loss
            match_loss = self._compute_match_loss(
                desc_b, desc_warped_b, uv_a, uv_b_matches)

            # 4. Compute non-matching loss
            non_match_loss = self._compute_non_match_loss(
                desc_b, desc_warped_b, uv_a_masked, uv_b_masked)

            # Accumulate losses
            total_match_loss += match_loss
            total_non_match_loss += non_match_loss

        # Average over batch
        total_match_loss = total_match_loss / B
        total_non_match_loss = total_non_match_loss / B

        # 5. Total loss
        lamda_d = self.params['lamda_d']
        total_loss = lamda_d * total_match_loss + total_non_match_loss

        return total_loss, {
            'total': total_loss.item(),
            'match': total_match_loss.item(),
            'non_match': total_non_match_loss.item(),
            'lamda_d': lamda_d
        }

    def compute_loss_vectorized(self,
                                descriptors: torch.Tensor,
                                descriptors_warped: torch.Tensor,
                                homographies: torch.Tensor,
                                mask_valid: Optional[torch.Tensor] = None,
                                cell_size: int = 8,
                                device: str = 'cpu') -> Tuple[torch.Tensor, Dict]:
        """
        Compute complete sparse loss with vectorized batch support (more efficient)

        Args:
            descriptors: Original descriptor tensor [B, D, Hc, Wc]
            descriptors_warped: Warped descriptor tensor [B, D, Hc, Wc]
            homographies: Homography matrices [B, 3, 3]
            mask_valid: Valid region mask [B, Hc, Wc] (optional)
            cell_size: Grid size
            device: Computing device ('cpu' or 'cuda')

        Returns:
            loss: Total loss (scalar)
            loss_components: Dictionary containing loss components
        """
        B, D, Hc, Wc = descriptors.shape

        # Generate grid coordinates (same for all batches)
        y_coords, x_coords = torch.meshgrid(torch.arange(Hc), torch.arange(Wc))
        coor_cells = torch.stack([y_coords, x_coords], dim=2).type(torch.FloatTensor).to(device)
        coor_cells = coor_cells.view(-1, 2)  # [Hc*Wc, 2]

        # Prepare for batch processing
        all_match_losses = []
        all_non_match_losses = []

        # Batch-wise coordinate transformation
        batch_uv_a = []
        batch_uv_b_matches = []
        batch_valid_counts = []

        for b in range(B):
            # Apply homography transformation for this batch
            uv_b_matches = self._warp_coordinates(
                coor_cells, homographies[b], Hc, Wc, device)

            # Filter valid matches
            valid_mask = (uv_b_matches[:, 0] >= 0) & (uv_b_matches[:, 0] < Hc) & \
                         (uv_b_matches[:, 1] >= 0) & (uv_b_matches[:, 1] < Wc)

            uv_a_valid = coor_cells[valid_mask]
            uv_b_valid = uv_b_matches[valid_mask]

            # Clamp coordinates
            uv_b_valid[:, 0] = torch.clamp(uv_b_valid[:, 0], 0, Hc - 1)
            uv_b_valid[:, 1] = torch.clamp(uv_b_valid[:, 1], 0, Wc - 1)

            batch_uv_a.append(uv_a_valid)
            batch_uv_b_matches.append(uv_b_valid)
            batch_valid_counts.append(len(uv_a_valid))

        # Compute losses for each batch item
        for b in range(B):
            if batch_valid_counts[b] == 0:
                all_match_losses.append(torch.tensor(0.0, device=device))
                all_non_match_losses.append(torch.tensor(0.0, device=device))
                continue

            # Generate non-matching points
            uv_a_masked, uv_b_masked = self._generate_non_matches(
                batch_uv_a[b], batch_uv_b_matches[b], Hc, Wc, device)

            # Compute matching loss
            match_loss = self._compute_match_loss_batch(
                descriptors[b], descriptors_warped[b],
                batch_uv_a[b], batch_uv_b_matches[b])

            # Compute non-matching loss
            non_match_loss = self._compute_non_match_loss_batch(
                descriptors[b], descriptors_warped[b],
                uv_a_masked, uv_b_masked)

            all_match_losses.append(match_loss)
            all_non_match_losses.append(non_match_loss)

        # Stack and average losses
        total_match_loss = torch.stack(all_match_losses).mean()
        total_non_match_loss = torch.stack(all_non_match_losses).mean()

        # Total loss
        lamda_d = self.params['lamda_d']
        total_loss = lamda_d * total_match_loss + total_non_match_loss

        return total_loss, {
            'total': total_loss.item(),
            'match': total_match_loss.item(),
            'non_match': total_non_match_loss.item(),
            'lamda_d': lamda_d,
            'batch_size': B
        }

    def _generate_matching_points(self, Hc: int, Wc: int,
                                  homographies: torch.Tensor,
                                  cell_size: int, device: str
                                  ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Generate matching point coordinate pairs (unchanged from original)"""
        # Generate grid coordinates
        y_coords, x_coords = torch.meshgrid(torch.arange(Hc), torch.arange(Wc))
        coor_cells = torch.stack([y_coords, x_coords], dim=2).type(torch.FloatTensor).to(device)

        # Adjust coordinate format
        coor_cells = coor_cells.view(-1, 2)

        # Apply homography transformation
        uv_b_matches = self._warp_coordinates(
            coor_cells, homographies, Hc, Wc, device)

        # Filter valid matches (coordinates within bounds)
        valid_mask = (uv_b_matches[:, 0] >= 0) & (uv_b_matches[:, 0] < Hc) & \
                     (uv_b_matches[:, 1] >= 0) & (uv_b_matches[:, 1] < Wc)

        uv_a_valid = coor_cells[valid_mask]
        uv_b_valid = uv_b_matches[valid_mask]

        # Clamp coordinates to be safe
        uv_b_valid[:, 0] = torch.clamp(uv_b_valid[:, 0], 0, Hc - 1)
        uv_b_valid[:, 1] = torch.clamp(uv_b_valid[:, 1], 0, Wc - 1)

        return uv_a_valid, uv_b_valid

    def _warp_coordinates(self, coor_cells: torch.Tensor,
                          homographies: torch.Tensor,
                          Hc: int, Wc: int,
                          device: str) -> torch.Tensor:
        """Generate transformed coordinates through homography transformation"""
        cell_size = 8  # Default cell size

        # Adjust coordinate format (y,x) -> (x,y)
        uv = torch.stack((coor_cells[:, 1], coor_cells[:, 0]), dim=1)

        # Normalize coordinates
        uv = normPts(uv, torch.tensor([Wc, Hc], device=device) * cell_size)

        # Apply homography transformation
        warped_uv = warp_points(uv, homographies, device)

        # Adjust back to (y,x) format
        warped_uv = torch.stack((warped_uv[:, 1], warped_uv[:, 0]), dim=1)

        # Denormalize
        warped_uv = denormPts(warped_uv, torch.tensor([Hc, Wc], device=device))

        return warped_uv

    def _generate_non_matches(self, uv_a: torch.Tensor,
                              uv_b_matches: torch.Tensor,
                              Hc: int, Wc: int,
                              device: str
                              ) -> Tuple[torch.Tensor, torch.Tensor]:
        """Generate non-matching point pairs (unchanged)"""
        # Sample non-matching points
        multiplier = self.params['num_masked_non_matches_per_match']
        uv_a_long = torch.repeat_interleave(uv_a, multiplier, dim=0)

        # Generate random non-matching points with correct bounds
        num_points = uv_a.shape[0] * multiplier
        y_coords = torch.randint(0, Hc, (num_points,), device=device).float()
        x_coords = torch.randint(0, Wc, (num_points,), device=device).float()
        non_match_indices = torch.stack([y_coords, x_coords], dim=1)

        return (uv_a_long, non_match_indices)

    def _compute_match_loss(self,
                            descriptors: torch.Tensor,
                            descriptors_warped: torch.Tensor,
                            uv_a: torch.Tensor,
                            uv_b: torch.Tensor) -> torch.Tensor:
        """Compute loss for matching point pairs (for single item)"""
        if uv_a.shape[0] == 0:
            return torch.tensor(0.0, device=descriptors.device)

        # Convert to 1D indices with bounds checking
        idx_a = (uv_a[:, 0] * descriptors.shape[2] + uv_a[:, 1]).long()
        idx_b = (uv_b[:, 0] * descriptors_warped.shape[2] + uv_b[:, 1]).long()

        # Clamp indices to valid range
        max_idx_a = descriptors.shape[1] * descriptors.shape[2] - 1
        max_idx_b = descriptors_warped.shape[1] * descriptors_warped.shape[2] - 1
        idx_a = torch.clamp(idx_a, 0, max_idx_a)
        idx_b = torch.clamp(idx_b, 0, max_idx_b)

        # Extract descriptors
        desc_a = descriptors.view(descriptors.shape[0], -1)[:, idx_a]
        desc_b = descriptors_warped.view(descriptors_warped.shape[0], -1)[:, idx_b]

        # Compute distance
        if self.params['dist'] == 'cos':
            # Cosine similarity
            dot_product = (desc_a * desc_b).sum(dim=0)
            norm_a = torch.norm(desc_a, p=2, dim=0)
            norm_b = torch.norm(desc_b, p=2, dim=0)
            return 1 - (dot_product / (norm_a * norm_b + 1e-8)).mean()
        else:
            # Euclidean distance
            return (desc_a - desc_b).pow(2).sum(dim=0).mean()

    def _compute_match_loss_batch(self,
                                  descriptors: torch.Tensor,
                                  descriptors_warped: torch.Tensor,
                                  uv_a: torch.Tensor,
                                  uv_b: torch.Tensor) -> torch.Tensor:
        """Compute loss for matching point pairs (batch version)"""
        return self._compute_match_loss(descriptors, descriptors_warped, uv_a, uv_b)

    def _compute_non_match_loss(self,
                                descriptors: torch.Tensor,
                                descriptors_warped: torch.Tensor,
                                uv_a: torch.Tensor,
                                uv_b: torch.Tensor) -> torch.Tensor:
        """Compute loss for non-matching point pairs"""
        if uv_a.shape[0] == 0:
            return torch.tensor(0.0, device=descriptors.device)

        # Convert to 1D indices with bounds checking
        idx_a = (uv_a[:, 0] * descriptors.shape[2] + uv_a[:, 1]).long()
        idx_b = (uv_b[:, 0] * descriptors_warped.shape[2] + uv_b[:, 1]).long()

        # Clamp indices to valid range
        max_idx_a = descriptors.shape[1] * descriptors.shape[2] - 1
        max_idx_b = descriptors_warped.shape[1] * descriptors_warped.shape[2] - 1
        idx_a = torch.clamp(idx_a, 0, max_idx_a)
        idx_b = torch.clamp(idx_b, 0, max_idx_b)

        # Extract descriptors
        desc_a = descriptors.view(descriptors.shape[0], -1)[:, idx_a]
        desc_b = descriptors_warped.view(descriptors_warped.shape[0], -1)[:, idx_b]

        # Compute distance
        if self.params['dist'] == 'cos':
            dist = (desc_a * desc_b).sum(dim=0)
        else:
            dist = (desc_a - desc_b).pow(2).sum(dim=0)

        # Compute negative sample loss
        margin = self.params['margin_neg']
        return torch.clamp(margin - dist, min=0).mean()

    def _compute_non_match_loss_batch(self,
                                      descriptors: torch.Tensor,
                                      descriptors_warped: torch.Tensor,
                                      uv_a: torch.Tensor,
                                      uv_b: torch.Tensor) -> torch.Tensor:
        """Compute loss for non-matching point pairs (batch version)"""
        return self._compute_non_match_loss(descriptors, descriptors_warped, uv_a, uv_b)


# Helper functions
def normPts(pts: torch.Tensor, size: torch.Tensor) -> torch.Tensor:
    """Normalize coordinates"""
    return pts / (size.unsqueeze(0).expand_as(pts)) * 2 - 1


def denormPts(pts: torch.Tensor, size: torch.Tensor) -> torch.Tensor:
    """Denormalize coordinates"""
    return ((pts + 1) / 2) * size.unsqueeze(0).expand_as(pts)


def warp_points(points: torch.Tensor, homographies: torch.Tensor, device: str) -> torch.Tensor:
    """Apply homography transformation to coordinate points"""
    num_points = points.shape[0]

    # Convert to homogeneous coordinates
    ones = torch.ones(num_points, 1, device=device)
    points_homogeneous = torch.cat([points.float(), ones], dim=1).transpose(0, 1)

    # Apply homography transformation
    if len(homographies.shape) == 2:
        # 2D homography matrix [3, 3]
        warped_points = torch.mm(homographies.float(), points_homogeneous)
    else:
        # 3D homography matrix [B, 3, 3]
        warped_points = torch.bmm(homographies.float(), points_homogeneous.unsqueeze(0))[0]

    # Normalize homogeneous coordinates
    warped_points = warped_points.transpose(0, 1)
    warped_points = warped_points / (warped_points[:, 2:3] + 1e-8)

    return warped_points[:, :2].float()


if __name__ == "__main__":
    # Test for batch SparseLoss class
    print("Testing Batch SparseLoss class...")

    # Setup device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")

    # Create test parameters
    B = 4  # batch size
    D = 128  # descriptor dimension
    Hc, Wc = 30, 40  # feature map size
    cell_size = 8

    # Generate random test data with batch dimension
    descriptors = torch.randn(B, D, Hc, Wc, device=device)
    descriptors_warped = torch.randn(B, D, Hc, Wc, device=device)

    # Create batch of homography matrices
    homographies = torch.eye(3, device=device).unsqueeze(0).repeat(B, 1, 1)
    # Add small random perturbations
    homographies[:, 0, 2] = torch.rand(B, device=device) * 0.2 - 0.1
    homographies[:, 1, 2] = torch.rand(B, device=device) * 0.2 - 0.1

    # Initialize loss module
    config = {
        'lamda_d': 250,
        'margin_pos': 1,
        'margin_neg': 0.2,
        'num_masked_non_matches_per_match': 10
    }
    loss_module = SparseLoss(config)

    try:
        # Test method 1: Sequential processing
        print("\nTesting sequential batch processing...")
        total_loss, loss_components = loss_module.compute_loss(
            descriptors, descriptors_warped, homographies,
            cell_size=cell_size, device=device
        )

        print("Sequential processing successful!")
        print(f"Total loss: {total_loss.item():.4f}")
        print(f"Match loss: {loss_components['match']:.4f}")
        print(f"Non-match loss: {loss_components['non_match']:.4f}")
        print(f"Lambda_d: {loss_components['lamda_d']}")

        # Test method 2: Vectorized processing
        print("\nTesting vectorized batch processing...")
        total_loss_vec, loss_components_vec = loss_module.compute_loss_vectorized(
            descriptors, descriptors_warped, homographies,
            cell_size=cell_size, device=device
        )

        print("Vectorized processing successful!")
        print(f"Total loss: {total_loss_vec.item():.4f}")
        print(f"Match loss: {loss_components_vec['match']:.4f}")
        print(f"Non-match loss: {loss_components_vec['non_match']:.4f}")
        print(f"Batch size: {loss_components_vec['batch_size']}")

        # Verify losses are reasonable
        assert total_loss.item() >= 0, "Loss should be non-negative"
        assert total_loss_vec.item() >= 0, "Vectorized loss should be non-negative"

        # Test with batch size 1
        print("\nTesting with batch size 1...")
        desc_single = descriptors[:1]
        desc_warped_single = descriptors_warped[:1]
        homo_single = homographies[:1]

        loss_single, _ = loss_module.compute_loss(
            desc_single, desc_warped_single, homo_single,
            cell_size=cell_size, device=device
        )
        print(f"Single batch loss: {loss_single.item():.4f}")

        print("\nAll tests passed!")

    except Exception as e:
        print(f"Test failed with error: {e}")
        raise